#!/usr/bin/env python3
"""
Test script to demonstrate score normalization detection.
"""

import pandas as pd
import tempfile
import os

def create_test_csv(scores, filename):
    """Create a test CSV file with given scores."""
    data = {
        'FEN': ['rnbqkbnr/pppppppp/8/8/8/8/PPPPPPPP/RNBQKBNR w KQkq - 0 1'] * len(scores),
        'Score': scores
    }
    df = pd.DataFrame(data)
    df.to_csv(filename, index=False)
    return filename

def test_score_detection():
    """Test the score normalization detection."""
    print("Testing Score Normalization Detection")
    print("=" * 50)
    
    # Test cases
    test_cases = [
        {
            'name': 'Centipawn scores (not normalized)',
            'scores': [0, 100, -150, 250, -300, 400, -500, 600],
            'expected_normalized': False
        },
        {
            'name': 'Normalized scores [-1, 1]',
            'scores': [0.0, 0.2, -0.3, 0.5, -0.7, 0.8, -0.9, 0.95],
            'expected_normalized': True
        },
        {
            'name': 'Mixed small values (ambiguous)',
            'scores': [0, 1, -1, 2, -2, 3, -3, 4],
            'expected_normalized': False  # Should be detected as centipawns
        },
        {
            'name': 'Large centipawn values',
            'scores': [0, 1000, -1200, 1500, -800, 2000, -2500, 3000],
            'expected_normalized': False
        }
    ]
    
    with tempfile.TemporaryDirectory() as temp_dir:
        for i, test_case in enumerate(test_cases):
            print(f"\nTest {i+1}: {test_case['name']}")
            print(f"Scores: {test_case['scores']}")
            
            # Create test CSV
            csv_file = os.path.join(temp_dir, f"test_{i}.csv")
            create_test_csv(test_case['scores'], csv_file)
            
            # Test detection logic
            data = pd.read_csv(csv_file, usecols=['FEN', 'Score'])
            
            # Implement the detection logic
            score_min = data['Score'].min()
            score_max = data['Score'].max()
            
            detected_normalized = False
            if score_min >= -1.1 and score_max <= 1.1:
                abs_scores = data['Score'].abs()
                median_abs_score = abs_scores.median()
                if median_abs_score < 1.0 and score_max > 0.5:
                    detected_normalized = True
            
            print(f"Range: [{score_min:.3f}, {score_max:.3f}]")
            print(f"Median absolute: {abs_scores.median():.3f}")
            print(f"Detected as normalized: {detected_normalized}")
            print(f"Expected: {test_case['expected_normalized']}")
            
            if detected_normalized == test_case['expected_normalized']:
                print("✓ PASS")
            else:
                print("✗ FAIL")
    
    print("\n" + "=" * 50)
    print("Summary:")
    print("• The code now auto-detects if scores are already normalized")
    print("• If detected as normalized, score scaling is automatically skipped")
    print("• You can still manually disable score scaling in the Configuration tab")
    print("• Detection criteria:")
    print("  - Scores must be in range [-1.1, 1.1]")
    print("  - Median absolute score < 1.0")
    print("  - Maximum score > 0.5")

if __name__ == "__main__":
    test_score_detection()
