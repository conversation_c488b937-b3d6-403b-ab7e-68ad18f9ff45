#!/usr/bin/env python3
"""
Test script to verify that the code correctly handles normalized scores between -1 and 1.
"""

import pandas as pd
import tempfile
import os
import sys

def test_normalized_score_handling():
    """Test that the ChessDataset correctly handles normalized scores."""
    print("Testing Normalized Score Handling")
    print("=" * 40)
    
    # Create test data with normalized scores
    test_data = {
        'FEN': [
            'rnbqkbnr/pppppppp/8/8/8/8/PPPPPPPP/RNBQKBNR w KQkq - 0 1',  # Starting position
            'rnbqkbnr/pppp1ppp/8/4p3/4P3/8/PPPP1PPP/RNBQKBNR w KQkq e6 0 2',  # e4 e5
            'rnbqkbnr/pppp1ppp/8/4p3/4P3/5N2/PPPP1PPP/RNBQKB1R b KQkq - 1 2',  # Nf3
        ],
        'Score': [0.0, 0.2, 0.35]  # Normalized scores between -1 and 1
    }
    
    # Create temporary CSV file
    with tempfile.NamedTemporaryFile(mode='w', suffix='.csv', delete=False) as f:
        df = pd.DataFrame(test_data)
        df.to_csv(f.name, index=False)
        csv_file = f.name
    
    try:
        print(f"Created test CSV with normalized scores: {test_data['Score']}")
        
        # Mock the required imports to avoid PyTorch dependency
        import types
        
        # Create mock modules
        mock_torch = types.ModuleType('torch')
        mock_torch.FloatTensor = lambda x: x  # Just return the input for testing
        
        sys.modules['torch'] = mock_torch
        sys.modules['torch.nn'] = types.ModuleType('torch.nn')
        sys.modules['torch.optim'] = types.ModuleType('torch.optim')
        sys.modules['torch.utils'] = types.ModuleType('torch.utils')
        sys.modules['torch.utils.data'] = types.ModuleType('torch.utils.data')
        sys.modules['chess'] = types.ModuleType('chess')
        
        # Mock chess.Board
        class MockBoard:
            def __init__(self, fen):
                self.fen = fen
                self.turn = True
                self.halfmove_clock = 0
                self.fullmove_number = 1
                self.ep_square = None
            
            def has_kingside_castling_rights(self, color):
                return True
            
            def has_queenside_castling_rights(self, color):
                return True
            
            def piece_at(self, square):
                return None  # Simplified for testing
        
        sys.modules['chess'].Board = MockBoard
        sys.modules['chess'].WHITE = True
        sys.modules['chess'].BLACK = False
        sys.modules['chess'].SQUARES = range(64)
        
        # Import and test ChessDataset
        from newNNue import ChessDataset
        
        # Create dataset
        dataset = ChessDataset(csv_file)
        
        print(f"✓ Dataset created successfully")
        print(f"✓ Dataset length: {len(dataset)}")
        
        # Test that scores are used directly without scaling
        for i in range(len(dataset)):
            features, score_tensor = dataset[i]
            original_score = test_data['Score'][i]
            returned_score = score_tensor[0] if hasattr(score_tensor, '__getitem__') else score_tensor
            
            print(f"✓ Sample {i}: Original score {original_score} → Returned score {returned_score}")
            
            # Verify score is unchanged (no scaling applied)
            if abs(float(returned_score) - original_score) < 0.0001:
                print(f"  ✓ Score correctly preserved (no scaling)")
            else:
                print(f"  ✗ Score was modified: expected {original_score}, got {returned_score}")
                return False
        
        print("\n" + "=" * 40)
        print("✓ All tests passed!")
        print("Key points verified:")
        print("• Scores between -1 and 1 are used directly")
        print("• No scaling or transformation is applied")
        print("• Dataset correctly loads normalized scores")
        print("• Code is simplified without unnecessary scaling logic")
        
        return True
        
    except Exception as e:
        print(f"✗ Error during testing: {e}")
        import traceback
        traceback.print_exc()
        return False
        
    finally:
        # Clean up temporary file
        try:
            os.unlink(csv_file)
        except:
            pass

def test_score_ranges():
    """Test various score ranges to ensure they're handled correctly."""
    print("\n" + "=" * 40)
    print("Testing Various Score Ranges")
    print("=" * 40)
    
    test_cases = [
        {
            'name': 'Typical normalized scores',
            'scores': [0.0, 0.2, -0.3, 0.5, -0.7, 0.8, -0.9, 0.95],
            'description': 'Common range for normalized chess evaluations'
        },
        {
            'name': 'Extreme normalized scores',
            'scores': [-1.0, -0.99, 0.99, 1.0],
            'description': 'Edge cases at the boundaries'
        },
        {
            'name': 'Small normalized scores',
            'scores': [0.0, 0.01, -0.02, 0.05, -0.1, 0.15],
            'description': 'Small values typical of balanced positions'
        }
    ]
    
    for case in test_cases:
        print(f"\nTest: {case['name']}")
        print(f"Description: {case['description']}")
        print(f"Score range: [{min(case['scores']):.3f}, {max(case['scores']):.3f}]")
        
        # Verify all scores are in valid range
        if all(-1.0 <= score <= 1.0 for score in case['scores']):
            print("✓ All scores are within [-1, 1] range")
        else:
            print("✗ Some scores are outside [-1, 1] range")
        
        # Check distribution
        avg_abs_score = sum(abs(s) for s in case['scores']) / len(case['scores'])
        print(f"✓ Average absolute score: {avg_abs_score:.3f}")
    
    print("\n✓ Score range testing completed")

def main():
    """Run all tests."""
    print("Testing Simplified Score Handling (Normalized Scores)")
    print("=" * 60)
    
    success = True
    
    # Test 1: Basic normalized score handling
    if not test_normalized_score_handling():
        success = False
    
    # Test 2: Score range validation
    test_score_ranges()
    
    print("\n" + "=" * 60)
    if success:
        print("✓ All tests passed! The code correctly handles normalized scores.")
        print("\nSummary of changes:")
        print("• Removed all score scaling/normalization logic")
        print("• Removed auto-detection of score ranges")
        print("• Simplified ChessDataset to use scores directly")
        print("• Removed score scaling GUI elements")
        print("• Code now assumes scores are already normalized [-1, 1]")
        return 0
    else:
        print("✗ Some tests failed.")
        return 1

if __name__ == "__main__":
    sys.exit(main())
