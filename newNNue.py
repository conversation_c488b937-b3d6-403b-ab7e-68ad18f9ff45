import os
import torch
import torch.nn as nn
import torch.optim as optim
from torch.utils.data import Dataset, DataLoader
import pandas as pd
import chess
import numpy as np
import tkinter as tk
from tkinter import filedialog, ttk, messagebox
import matplotlib.pyplot as plt
from matplotlib.backends.backend_tkagg import FigureCanvasTkAgg
import shutil
import glob
from datetime import datetime
import threading
import queue
import logging
from pathlib import Path
import json
from typing import List, Tuple, Optional, Dict, Any

# Configure logging
logging.basicConfig(level=logging.INFO, format='%(asctime)s - %(levelname)s - %(message)s')
logger = logging.getLogger(__name__)

class ChessDataset(Dataset):
    """Optimized Chess Dataset with caching and better memory management."""
    
    def __init__(self, csv_file: str, cache_size: int = 10000):
        self.csv_file = csv_file
        self.cache_size = cache_size
        self.cache = {}
        self.cache_order = []
        
        # Load data efficiently
        self.data = pd.read_csv(csv_file, usecols=['FEN', 'Score'])
        
        # Pre-compute piece mapping for efficiency
        self.piece_to_idx = {
            'P': 0, 'N': 1, 'B': 2, 'R': 3, 'Q': 4, 'K': 5,
            'p': 6, 'n': 7, 'b': 8, 'r': 9, 'q': 10, 'k': 11
        }
        
        logger.info(f"Loaded dataset with {len(self.data)} samples from {csv_file}")
        
    def __len__(self) -> int:
        return len(self.data)
    
    def __getitem__(self, idx: int) -> Tuple[torch.Tensor, torch.Tensor]:
        # Check cache first
        if idx in self.cache:
            return self.cache[idx]
        
        row = self.data.iloc[idx]
        fen = row['FEN']
        score = row['Score']
        
        try:
            board = chess.Board(fen)
            features = self.fen_to_features(board)
            
            result = (torch.FloatTensor(features), torch.FloatTensor([score]))
            
            # Cache management
            if len(self.cache) >= self.cache_size:
                oldest_idx = self.cache_order.pop(0)
                del self.cache[oldest_idx]
            
            self.cache[idx] = result
            self.cache_order.append(idx)
            
            return result
            
        except Exception as e:
            logger.warning(f"Error processing FEN at index {idx}: {fen}. Error: {e}")
            # Return zero features and score for invalid positions
            return torch.zeros(768), torch.FloatTensor([0.0])
    
    def fen_to_features(self, board: chess.Board) -> np.ndarray:
        """Convert board position to feature vector with additional features."""
        # Basic piece placement (768 features: 64 squares * 12 piece types)
        features = np.zeros(768 + 8, dtype=np.float32)  # Extra features for game state
        
        # Piece placement
        for square in chess.SQUARES:
            piece = board.piece_at(square)
            if piece:
                piece_type = str(piece)
                if piece_type in self.piece_to_idx:
                    idx = self.piece_to_idx[piece_type] * 64 + square
                    features[idx] = 1.0
        
        # Additional game state features (last 8 positions)
        features[768] = float(board.turn)  # Side to move
        features[769] = float(board.has_kingside_castling_rights(chess.WHITE))
        features[770] = float(board.has_queenside_castling_rights(chess.WHITE))
        features[771] = float(board.has_kingside_castling_rights(chess.BLACK))
        features[772] = float(board.has_queenside_castling_rights(chess.BLACK))
        features[773] = float(board.ep_square is not None)  # En passant available
        features[774] = min(board.halfmove_clock / 50.0, 1.0)  # Normalized halfmove clock
        features[775] = min(board.fullmove_number / 100.0, 1.0)  # Normalized move number
        
        return features

class ImprovedNNUE(nn.Module):
    """Enhanced NNUE architecture with better regularization and architecture."""
    
    def __init__(self, dropout_rate: float = 0.1):
        super(ImprovedNNUE, self).__init__()
        
        # Feature transformer
        self.feature_transformer = nn.Sequential(
            nn.Linear(776, 512),  # 768 + 8 additional features
            nn.ReLU(),
            nn.Dropout(dropout_rate)
        )
        
        # Hidden layers with residual connections
        self.hidden1 = nn.Linear(512, 256)
        self.hidden2 = nn.Linear(256, 256)
        self.hidden3 = nn.Linear(256, 128)
        
        # Output layer
        self.output = nn.Linear(128, 1)
        
        # Activation functions
        self.relu = nn.ReLU()
        self.dropout = nn.Dropout(dropout_rate)
        
        # Initialize weights
        self._initialize_weights()
        
    def _initialize_weights(self):
        """Initialize weights using Xavier/Glorot initialization."""
        for module in self.modules():
            if isinstance(module, nn.Linear):
                nn.init.xavier_uniform_(module.weight)
                nn.init.zeros_(module.bias)
    
    def forward(self, x: torch.Tensor) -> torch.Tensor:
        # Feature transformation
        x = self.feature_transformer(x)
        
        # Hidden layers with residual connections
        h1 = self.dropout(self.relu(self.hidden1(x)))
        h2 = self.dropout(self.relu(self.hidden2(h1)))
        h2 = h2 + h1  # Residual connection
        
        h3 = self.dropout(self.relu(self.hidden3(h2)))
        
        # Output
        return self.output(h3)

class TrainingConfig:
    """Configuration class for training parameters."""

    def __init__(self):
        self.epochs_per_file = 15
        self.batch_size = 2048
        self.learning_rate = 0.001
        self.weight_decay = 1e-5
        self.validation_interval = 5
        self.checkpoint_interval = 10
        self.scheduler_patience = 5
        self.scheduler_factor = 0.5
        self.early_stopping_patience = 2000
        self.gradient_clip_value = 1.0
        # New configuration for epoch-based saving
        self.save_model_epochs = [50,70,90,100,120,150,180,200,250,300,350,400,450,500,550,600,650,700,750,800,850,900,950,1000,1050,1100,1150,1200,1250,1300,1350,1400,1450,1500,1550,1600,1650,1700,1750,1800,1850,1900,1950,2000,2050,2100,2150,2200,2250,2300,2350,2400,2450,2500,2550,2600,2650,2700,2750,2800,2850,2900,2950,3000,3050,3100,3150,3200,3250,3300,3350,3400,3450,3500,3550,3600,3650,3700,3750,3800,3850,3900,3950,4000,4050,4100,4150,4200,4250,4300,4350,4400,4450,4500,4550,4600,4650,4700,4750,4800,4850,4900,4950,5000 ]  # List of epochs to save models at
        self.auto_save_best = False  # Disable automatic best model saving
        self.auto_load_checkpoint = True  # Automatically load latest checkpoint when starting training
        
    def to_dict(self) -> Dict[str, Any]:
        return self.__dict__
    
    def from_dict(self, config_dict: Dict[str, Any]):
        for key, value in config_dict.items():
            if hasattr(self, key):
                setattr(self, key, value)

class NNUE_Trainer_App:
    """Enhanced NNUE Trainer with better architecture and threading."""
    
    def __init__(self, root):
        self.root = root
        self.root.title("Enhanced NNUE Chess Trainer")
        self.root.geometry("1200x1000")
        
        # Core components
        self.device = torch.device('cuda' if torch.cuda.is_available() else 'cpu')
        logger.info(f"Using device: {self.device}")
        
        # Training state
        self.model: Optional[ImprovedNNUE] = None
        self.optimizer: Optional[torch.optim.Optimizer] = None
        self.scheduler: Optional[torch.optim.lr_scheduler.ReduceLROnPlateau] = None
        self.criterion = nn.MSELoss()
        self.config = TrainingConfig()
        
        # File management
        self.training_files: List[str] = []
        self.current_file_index = 0
        self.checkpoint_folder = Path("checkpoints")
        self.checkpoint_folder.mkdir(exist_ok=True)
        
        # Training history
        self.epochs_completed = 0
        self.train_loss_history: List[float] = []
        self.val_loss_history: List[float] = []
        self.lr_history: List[float] = []
        self.best_val_loss = float('inf')
        self.epochs_without_improvement = 0
        
        # Threading
        self.training_thread: Optional[threading.Thread] = None
        self.stop_training_flag = threading.Event()
        self.training_queue = queue.Queue()
        
        # GUI setup
        self.setup_gui()
        self.setup_menu()
        
        # Start queue processor
        self.process_queue()
        
    def setup_menu(self):
        """Setup application menu."""
        menubar = tk.Menu(self.root)
        self.root.config(menu=menubar)
        
        # File menu
        file_menu = tk.Menu(menubar, tearoff=0)
        menubar.add_cascade(label="File", menu=file_menu)
        file_menu.add_command(label="Save Config", command=self.save_config)
        file_menu.add_command(label="Load Config", command=self.load_config)
        file_menu.add_separator()
        file_menu.add_command(label="Export Model", command=self.export_model)
        file_menu.add_command(label="Import Model", command=self.import_model)
        
        # Tools menu
        tools_menu = tk.Menu(menubar, tearoff=0)
        menubar.add_cascade(label="Tools", menu=tools_menu)
        tools_menu.add_command(label="Evaluate Position", command=self.evaluate_position)
        tools_menu.add_command(label="Model Info", command=self.show_model_info)
        tools_menu.add_command(label="Saved Models Info", command=self.show_saved_models_info)
        
    def setup_gui(self):
        """Setup the GUI components."""
        # Create notebook for tabs
        notebook = ttk.Notebook(self.root)
        notebook.pack(fill="both", expand=True, padx=10, pady=5)
        
        # Training tab
        training_frame = ttk.Frame(notebook)
        notebook.add(training_frame, text="Training")
        self.setup_training_tab(training_frame)
        
        # Configuration tab
        config_frame = ttk.Frame(notebook)
        notebook.add(config_frame, text="Configuration")
        self.setup_config_tab(config_frame)
        
        # Analysis tab
        analysis_frame = ttk.Frame(notebook)
        notebook.add(analysis_frame, text="Analysis")
        self.setup_analysis_tab(analysis_frame)
        
    def setup_training_tab(self, parent):
        """Setup the training tab."""
        # File selection frame
        file_frame = tk.LabelFrame(parent, text="File Selection", padx=5, pady=5)
        file_frame.pack(fill="x", padx=5, pady=5)
        
        # Training files
        tk.Label(file_frame, text="Training CSV Folder:").grid(row=0, column=0, sticky="w")
        self.train_folder_entry = tk.Entry(file_frame, width=60)
        self.train_folder_entry.grid(row=0, column=1, padx=5)
        tk.Button(file_frame, text="Browse", command=self.select_train_folder).grid(row=0, column=2)
        
        # Other file selections...
        tk.Label(file_frame, text="Processed Folder:").grid(row=1, column=0, sticky="w")
        self.processed_folder_entry = tk.Entry(file_frame, width=60)
        self.processed_folder_entry.grid(row=1, column=1, padx=5)
        tk.Button(file_frame, text="Browse", command=self.select_processed_folder).grid(row=1, column=2)
        
        tk.Label(file_frame, text="Validation CSV:").grid(row=2, column=0, sticky="w")
        self.val_file_entry = tk.Entry(file_frame, width=60)
        self.val_file_entry.grid(row=2, column=1, padx=5)
        tk.Button(file_frame, text="Browse", command=self.select_val_file).grid(row=2, column=2)
        
        # Control buttons
        button_frame = tk.Frame(parent)
        button_frame.pack(fill="x", padx=5, pady=5)
        
        self.train_button = tk.Button(button_frame, text="Start Training", 
                                     command=self.start_training, bg="green", fg="white")
        self.train_button.pack(side="left", padx=5)
        
        self.stop_button = tk.Button(button_frame, text="Stop Training", 
                                    command=self.stop_training, state="disabled", bg="red", fg="white")
        self.stop_button.pack(side="left", padx=5)
        
        self.validate_button = tk.Button(button_frame, text="Run Validation", command=self.run_validation)
        self.validate_button.pack(side="left", padx=5)

        self.load_latest_button = tk.Button(button_frame, text="Load Latest Checkpoint", command=self.load_latest_checkpoint_manual)
        self.load_latest_button.pack(side="left", padx=5)

        self.reset_files_button = tk.Button(button_frame, text="Reset File Progress", command=self.reset_file_progress)
        self.reset_files_button.pack(side="left", padx=5)
        
        # Progress and status
        progress_frame = tk.Frame(parent)
        progress_frame.pack(fill="x", padx=5, pady=5)
        
        tk.Label(progress_frame, text="Progress:").pack(anchor="w")
        self.progress = ttk.Progressbar(progress_frame, orient="horizontal", mode="determinate")
        self.progress.pack(fill="x", pady=2)
        
        self.status_label = tk.Label(progress_frame, text="Ready", bd=1, relief="sunken", anchor="w")
        self.status_label.pack(fill="x", pady=2)
        
        # Training graph
        graph_frame = tk.LabelFrame(parent, text="Training Progress", padx=5, pady=5)
        graph_frame.pack(fill="both", expand=True, padx=5, pady=5)
        
        self.fig, (self.ax1, self.ax2) = plt.subplots(2, 1, figsize=(10, 8))
        self.canvas = FigureCanvasTkAgg(self.fig, master=graph_frame)
        self.canvas.get_tk_widget().pack(fill="both", expand=True)
        
        self.update_plot()
        
    def setup_config_tab(self, parent):
        """Setup the configuration tab."""
        # Training parameters
        param_frame = tk.LabelFrame(parent, text="Training Parameters", padx=5, pady=5)
        param_frame.pack(fill="x", padx=5, pady=5)
        
        # Create parameter entries
        self.param_entries = {}
        params = [
            ("Epochs per file:", "epochs_per_file", str(self.config.epochs_per_file)),
            ("Batch size:", "batch_size", str(self.config.batch_size)),
            ("Learning rate:", "learning_rate", str(self.config.learning_rate)),
            ("Weight decay:", "weight_decay", str(self.config.weight_decay)),
            ("Validation interval:", "validation_interval", str(self.config.validation_interval)),
            ("Checkpoint interval:", "checkpoint_interval", str(self.config.checkpoint_interval)),
            ("Scheduler patience:", "scheduler_patience", str(self.config.scheduler_patience)),
            ("Early stopping patience:", "early_stopping_patience", str(self.config.early_stopping_patience)),
        ]
        
        for i, (label, key, default) in enumerate(params):
            row, col = divmod(i, 2)
            tk.Label(param_frame, text=label).grid(row=row, column=col*3, sticky="w", padx=5, pady=2)
            entry = tk.Entry(param_frame, width=15)
            entry.grid(row=row, column=col*3+1, padx=5, pady=2)
            entry.insert(0, default)
            self.param_entries[key] = entry

        # Add new epoch-based saving parameters
        next_row = len(params)//2 + 1

        # Save model epochs
        tk.Label(param_frame, text="Save model at epochs:").grid(row=next_row, column=0, sticky="w", padx=5, pady=2)
        self.save_epochs_entry = tk.Entry(param_frame, width=40)
        self.save_epochs_entry.grid(row=next_row, column=1, columnspan=3, padx=5, pady=2)
        self.save_epochs_entry.insert(0, ",".join(map(str, self.config.save_model_epochs)))

        # Auto save best checkbox
        self.auto_save_best_var = tk.BooleanVar(value=self.config.auto_save_best)
        tk.Checkbutton(param_frame, text="Auto-save best model",
                      variable=self.auto_save_best_var).grid(row=next_row+1, column=0, columnspan=2, sticky="w", padx=5, pady=2)

        # Auto load checkpoint checkbox
        self.auto_load_checkpoint_var = tk.BooleanVar(value=self.config.auto_load_checkpoint)
        tk.Checkbutton(param_frame, text="Auto-load latest checkpoint on start",
                      variable=self.auto_load_checkpoint_var).grid(row=next_row+1, column=2, columnspan=2, sticky="w", padx=5, pady=2)

        # Apply button
        tk.Button(param_frame, text="Apply Configuration",
                 command=self.apply_config).grid(row=next_row+2, column=0, columnspan=6, pady=10)

        # Add help text
        help_text = tk.Label(param_frame,
                           text="Epoch-based saving: Enter comma-separated epochs (e.g., 50,100,200,500)\n"
                                "Auto-save best: When enabled, automatically saves the model with best validation loss\n"
                                "Auto-load checkpoint: When enabled, automatically loads the latest checkpoint when starting training",
                           justify="left", fg="gray")
        help_text.grid(row=next_row+3, column=0, columnspan=6, pady=5, sticky="w")
        
    def setup_analysis_tab(self, parent):
        """Setup the analysis tab."""
        # Position evaluation
        eval_frame = tk.LabelFrame(parent, text="Position Evaluation", padx=5, pady=5)
        eval_frame.pack(fill="x", padx=5, pady=5)
        
        tk.Label(eval_frame, text="FEN:").grid(row=0, column=0, sticky="w")
        self.fen_entry = tk.Entry(eval_frame, width=80)
        self.fen_entry.grid(row=0, column=1, padx=5)
        self.fen_entry.insert(0, "rnbqkbnr/pppppppp/8/8/8/8/PPPPPPPP/RNBQKBNR w KQkq - 0 1")
        
        tk.Button(eval_frame, text="Evaluate", command=self.evaluate_position).grid(row=0, column=2, padx=5)
        
        self.eval_result_label = tk.Label(eval_frame, text="Evaluation: Not calculated", anchor="w")
        self.eval_result_label.grid(row=1, column=0, columnspan=3, sticky="w", pady=5)
        
    def apply_config(self):
        """Apply configuration changes."""
        try:
            for key, entry in self.param_entries.items():
                value = entry.get()
                if hasattr(self.config, key):
                    # Type conversion based on original type
                    original_value = getattr(self.config, key)
                    if isinstance(original_value, int):
                        setattr(self.config, key, int(value))
                    elif isinstance(original_value, float):
                        setattr(self.config, key, float(value))
                    else:
                        setattr(self.config, key, value)

            # Handle new epoch-based saving parameters
            epochs_str = self.save_epochs_entry.get().strip()
            if epochs_str:
                # Parse comma-separated epochs
                epochs = [int(x.strip()) for x in epochs_str.split(',') if x.strip().isdigit()]
                self.config.save_model_epochs = sorted(epochs)  # Sort for easier processing
            else:
                self.config.save_model_epochs = []

            # Handle auto save best checkbox
            self.config.auto_save_best = self.auto_save_best_var.get()

            # Handle auto load checkpoint checkbox
            self.config.auto_load_checkpoint = self.auto_load_checkpoint_var.get()

            self.status_label.config(text="Configuration applied successfully")
            logger.info(f"Configuration updated - Save epochs: {self.config.save_model_epochs}, Auto-save best: {self.config.auto_save_best}, Auto-load checkpoint: {self.config.auto_load_checkpoint}")

        except Exception as e:
            messagebox.showerror("Error", f"Failed to apply configuration: {e}")
    
    def save_config(self):
        """Save current configuration to file."""
        file_path = filedialog.asksaveasfilename(
            defaultextension=".json",
            filetypes=[("JSON files", "*.json"), ("All files", "*.*")]
        )
        if file_path:
            try:
                with open(file_path, 'w') as f:
                    json.dump(self.config.to_dict(), f, indent=2)
                self.status_label.config(text=f"Configuration saved to {file_path}")
            except Exception as e:
                messagebox.showerror("Error", f"Failed to save configuration: {e}")
    
    def load_config(self):
        """Load configuration from file."""
        file_path = filedialog.askopenfilename(
            filetypes=[("JSON files", "*.json"), ("All files", "*.*")]
        )
        if file_path:
            try:
                with open(file_path, 'r') as f:
                    config_dict = json.load(f)
                self.config.from_dict(config_dict)

                # Update GUI entries
                for key, entry in self.param_entries.items():
                    if hasattr(self.config, key):
                        entry.delete(0, tk.END)
                        entry.insert(0, str(getattr(self.config, key)))

                # Update new epoch-based saving parameters
                self.save_epochs_entry.delete(0, tk.END)
                self.save_epochs_entry.insert(0, ",".join(map(str, self.config.save_model_epochs)))
                self.auto_save_best_var.set(self.config.auto_save_best)
                self.auto_load_checkpoint_var.set(self.config.auto_load_checkpoint)

                self.status_label.config(text=f"Configuration loaded from {file_path}")
            except Exception as e:
                messagebox.showerror("Error", f"Failed to load configuration: {e}")
    
    def initialize_model(self):
        """Initialize model and optimizer."""
        if self.model is None:
            self.model = ImprovedNNUE().to(self.device)
            self.optimizer = optim.AdamW(
                self.model.parameters(),
                lr=self.config.learning_rate,
                weight_decay=self.config.weight_decay
            )
            self.scheduler = optim.lr_scheduler.ReduceLROnPlateau(
                self.optimizer,
                mode='min',
                factor=self.config.scheduler_factor,
                patience=self.config.scheduler_patience,
                verbose=True
            )
            logger.info("Model initialized successfully")
    
    def start_training(self):
        """Start training in a separate thread."""
        if not self.training_files:
            messagebox.showerror("Error", "No training files selected!")
            return

        if not self.processed_folder_entry.get():
            messagebox.showerror("Error", "Please select a processed folder!")
            return

        self.apply_config()

        # Try to automatically load the most recent checkpoint if enabled
        if self.config.auto_load_checkpoint and self.auto_load_latest_checkpoint():
            self.status_label.config(text="Loaded latest checkpoint, continuing training...")
            logger.info("Automatically loaded latest checkpoint")
        else:
            self.initialize_model()
            if self.config.auto_load_checkpoint:
                self.status_label.config(text="No checkpoint found, starting fresh training...")
                logger.info("No checkpoint found, starting fresh training")
            else:
                self.status_label.config(text="Auto-load disabled, starting fresh training...")
                logger.info("Auto-load checkpoint disabled, starting fresh training")

        # Reset file index if we have new training files (important for continuing training with new files)
        if self.training_files and self.current_file_index >= len(self.training_files):
            self.current_file_index = 0
            logger.info(f"Reset file index to 0 - found {len(self.training_files)} training files")
            self.status_label.config(text=f"Reset to process {len(self.training_files)} training files from beginning")

        # Reset training state
        self.stop_training_flag.clear()

        # Update UI
        self.train_button.config(state="disabled")
        self.stop_button.config(state="normal")

        # Start training thread
        self.training_thread = threading.Thread(target=self.training_loop, daemon=True)
        self.training_thread.start()
    
    def training_loop(self):
        """Main training loop running in separate thread."""
        try:
            logger.info(f"Starting training loop - current_file_index: {self.current_file_index}, total_files: {len(self.training_files)}")

            if self.current_file_index >= len(self.training_files):
                self.training_queue.put(('status', f"No files to process - current index {self.current_file_index} >= total files {len(self.training_files)}"))
                logger.warning(f"No files to process - current_file_index ({self.current_file_index}) >= len(training_files) ({len(self.training_files)})")
                return

            for file_idx in range(self.current_file_index, len(self.training_files)):
                if self.stop_training_flag.is_set():
                    break
                
                current_file = self.training_files[file_idx]
                self.training_queue.put(('status', f"Training on {os.path.basename(current_file)}..."))
                
                # Load dataset
                dataset = ChessDataset(current_file)
                train_loader = DataLoader(
                    dataset, 
                    batch_size=self.config.batch_size, 
                    shuffle=True,
                    num_workers=2,
                    pin_memory=True if self.device.type == 'cuda' else False
                )
                
                # Train for specified epochs
                for epoch in range(self.config.epochs_per_file):
                    if self.stop_training_flag.is_set():
                        break
                    
                    train_loss = self.train_epoch(train_loader)
                    self.train_loss_history.append(train_loss)
                    self.lr_history.append(self.optimizer.param_groups[0]['lr'])
                    self.epochs_completed += 1
                    
                    # Update progress
                    progress = (file_idx / len(self.training_files) * 100) + \
                              ((epoch + 1) / self.config.epochs_per_file * (100 / len(self.training_files)))
                    
                    self.training_queue.put(('progress', progress))
                    self.training_queue.put(('status', 
                        f"File {file_idx+1}/{len(self.training_files)} - "
                        f"Epoch {self.epochs_completed} - Loss: {train_loss:.6f} - "
                        f"LR: {self.optimizer.param_groups[0]['lr']:.6f}"))
                    
                    # Validation
                    if (self.config.validation_interval > 0 and 
                        self.epochs_completed % self.config.validation_interval == 0):
                        self.run_validation_threaded()
                    
                    # Checkpoint
                    if (self.config.checkpoint_interval > 0 and
                        self.epochs_completed % self.config.checkpoint_interval == 0):
                        self.save_checkpoint()

                    # Epoch-based model saving
                    if self.epochs_completed in self.config.save_model_epochs:
                        self.save_epoch_model(self.epochs_completed)

                    # Early stopping check
                    if self.epochs_without_improvement >= self.config.early_stopping_patience:
                        self.training_queue.put(('status', "Early stopping triggered"))
                        self.stop_training_flag.set()
                        break
                    
                    # Update plots
                    self.training_queue.put(('plot_update', None))
                
                # Move processed file
                if not self.stop_training_flag.is_set():
                    processed_folder = self.processed_folder_entry.get()
                    if processed_folder:
                        dest = os.path.join(processed_folder, os.path.basename(current_file))
                        shutil.move(current_file, dest)
                
                self.current_file_index = file_idx + 1
            
            # Training completed
            if not self.stop_training_flag.is_set():
                self.training_queue.put(('status', "Training completed on all files!"))
            
        except Exception as e:
            logger.error(f"Training error: {e}")
            self.training_queue.put(('error', str(e)))
        
        finally:
            self.training_queue.put(('training_finished', None))
    
    def train_epoch(self, train_loader) -> float:
        """Train for one epoch."""
        self.model.train()
        total_loss = 0.0
        num_batches = 0
        
        for features, scores in train_loader:
            if self.stop_training_flag.is_set():
                break
            
            features, scores = features.to(self.device, non_blocking=True), scores.to(self.device, non_blocking=True)
            
            self.optimizer.zero_grad()
            outputs = self.model(features)
            loss = self.criterion(outputs, scores)
            loss.backward()
            
            # Gradient clipping
            torch.nn.utils.clip_grad_norm_(self.model.parameters(), self.config.gradient_clip_value)
            
            self.optimizer.step()
            
            total_loss += loss.item()
            num_batches += 1
        
        return total_loss / max(num_batches, 1)
    
    def run_validation_threaded(self):
        """Run validation in training thread."""
        val_file = self.val_file_entry.get()
        if not val_file or not os.path.exists(val_file):
            return
        
        try:
            self.model.eval()
            dataset = ChessDataset(val_file)
            val_loader = DataLoader(dataset, batch_size=self.config.batch_size, num_workers=2)
            
            val_loss = 0.0
            num_batches = 0
            
            with torch.no_grad():
                for features, scores in val_loader:
                    features, scores = features.to(self.device, non_blocking=True), scores.to(self.device, non_blocking=True)
                    outputs = self.model(features)
                    val_loss += self.criterion(outputs, scores).item()
                    num_batches += 1
            
            avg_val_loss = val_loss / max(num_batches, 1)
            self.val_loss_history.append(avg_val_loss)
            
            # Learning rate scheduling
            self.scheduler.step(avg_val_loss)
            
            # Check for improvement
            if avg_val_loss < self.best_val_loss:
                self.best_val_loss = avg_val_loss
                self.epochs_without_improvement = 0
                # Save best model only if auto_save_best is enabled
                if self.config.auto_save_best:
                    self.save_best_model()
            else:
                self.epochs_without_improvement += 1
            
            self.training_queue.put(('status', f"Validation completed - Loss: {avg_val_loss:.6f}"))
            
        except Exception as e:
            logger.error(f"Validation error: {e}")
    
    def save_best_model(self):
        """Save the best model."""
        if self.model:
            best_model_path = self.checkpoint_folder / "best_model.pt"
            torch.save({
                'model_state_dict': self.model.state_dict(),
                'val_loss': self.best_val_loss,
                'epoch': self.epochs_completed,
                'config': self.config.to_dict()
            }, best_model_path)

    def save_epoch_model(self, epoch: int):
        """Save model at specified epoch."""
        if self.model:
            epoch_model_path = self.checkpoint_folder / f"model_epoch_{epoch}.pt"

            # Get current validation loss if available
            current_val_loss = self.val_loss_history[-1] if self.val_loss_history else None

            model_data = {
                'model_state_dict': self.model.state_dict(),
                'epoch': epoch,
                'train_loss': self.train_loss_history[-1] if self.train_loss_history else None,
                'val_loss': current_val_loss,
                'best_val_loss': self.best_val_loss,
                'config': self.config.to_dict(),
                'timestamp': datetime.now().isoformat(),
                'optimizer_state_dict': self.optimizer.state_dict(),
                'scheduler_state_dict': self.scheduler.state_dict() if self.scheduler else None
            }

            torch.save(model_data, epoch_model_path)

            self.training_queue.put(('status', f"Model saved at epoch {epoch}: {epoch_model_path.name}"))
            logger.info(f"Epoch-based model saved: {epoch_model_path}")
    
    def process_queue(self):
        """Process messages from training thread."""
        try:
            while True:
                try:
                    message_type, data = self.training_queue.get_nowait()
                    
                    if message_type == 'status':
                        self.status_label.config(text=data)
                    elif message_type == 'progress':
                        self.progress['value'] = data
                    elif message_type == 'plot_update':
                        self.update_plot()
                    elif message_type == 'error':
                        messagebox.showerror("Training Error", data)
                    elif message_type == 'training_finished':
                        self.train_button.config(state="normal")
                        self.stop_button.config(state="disabled")
                    
                except queue.Empty:
                    break
        
        except Exception as e:
            logger.error(f"Queue processing error: {e}")
        
        # Schedule next check
        self.root.after(100, self.process_queue)
    
    def update_plot(self):
        """Update training plots."""
        self.ax1.clear()
        self.ax2.clear()
        
        # Loss plot
        if self.train_loss_history:
            epochs = range(1, len(self.train_loss_history) + 1)
            self.ax1.plot(epochs, self.train_loss_history, label='Training Loss', alpha=0.7)
        
        if self.val_loss_history:
            val_epochs = [i * self.config.validation_interval for i in range(1, len(self.val_loss_history) + 1)]
            self.ax1.plot(val_epochs, self.val_loss_history, label='Validation Loss', marker='o', linewidth=2)
        
        self.ax1.set_xlabel('Epochs')
        self.ax1.set_ylabel('Loss')
        self.ax1.set_title('Training and Validation Loss')
        self.ax1.legend()
        self.ax1.grid(True, alpha=0.3)
        
        # Learning rate plot
        if self.lr_history:
            self.ax2.plot(range(1, len(self.lr_history) + 1), self.lr_history, color='red', alpha=0.7)
            self.ax2.set_xlabel('Epochs')
            self.ax2.set_ylabel('Learning Rate')
            self.ax2.set_title('Learning Rate Schedule')
            self.ax2.grid(True, alpha=0.3)
            self.ax2.set_yscale('log')
        
        plt.tight_layout()
        self.canvas.draw()
    
    def stop_training(self):
        """Stop training gracefully."""
        self.stop_training_flag.set()
        self.stop_button.config(state="disabled")
        self.status_label.config(text="Stopping training...")
    
    def save_checkpoint(self):
        """Save training checkpoint."""
        if not self.model:
            return
        
        try:
            checkpoint = {
                'epoch': self.epochs_completed,
                'model_state_dict': self.model.state_dict(),
                'optimizer_state_dict': self.optimizer.state_dict(),
                'scheduler_state_dict': self.scheduler.state_dict() if self.scheduler else None,
                'train_loss_history': self.train_loss_history,
                'val_loss_history': self.val_loss_history,
                'lr_history': self.lr_history,
                'best_val_loss': self.best_val_loss,
                'epochs_without_improvement': self.epochs_without_improvement,
                'current_file_index': self.current_file_index,
                'config': self.config.to_dict(),
                'timestamp': datetime.now().isoformat()
            }
            
            filename = self.checkpoint_folder / f"checkpoint_epoch_{self.epochs_completed}.pt"
            torch.save(checkpoint, filename)
            
            # Keep only last 5 checkpoints to save space
            self.cleanup_old_checkpoints()
            
            logger.info(f"Checkpoint saved: {filename}")
            
        except Exception as e:
            logger.error(f"Failed to save checkpoint: {e}")
    
    def cleanup_old_checkpoints(self):
        """Remove old checkpoints, keeping only the most recent ones."""
        checkpoints = sorted(self.checkpoint_folder.glob("checkpoint_epoch_*.pt"))
        if len(checkpoints) > 5:
            for old_checkpoint in checkpoints[:-5]:
                old_checkpoint.unlink()
                logger.info(f"Removed old checkpoint: {old_checkpoint}")
    
    def load_checkpoint(self):
        """Load training checkpoint."""
        file_path = filedialog.askopenfilename(
            initialdir=self.checkpoint_folder,
            title="Select Checkpoint File",
            filetypes=[("PyTorch files", "*.pt"), ("All files", "*.*")]
        )
        
        if not file_path:
            return
        
        try:
            checkpoint = torch.load(file_path, map_location=self.device)
            
            # Initialize model if needed
            self.initialize_model()
            
            # Load states
            self.model.load_state_dict(checkpoint['model_state_dict'])
            self.optimizer.load_state_dict(checkpoint['optimizer_state_dict'])
            
            if 'scheduler_state_dict' in checkpoint and checkpoint['scheduler_state_dict']:
                self.scheduler.load_state_dict(checkpoint['scheduler_state_dict'])
            
            # Restore training history
            self.epochs_completed = checkpoint.get('epoch', 0)
            self.train_loss_history = checkpoint.get('train_loss_history', [])
            self.val_loss_history = checkpoint.get('val_loss_history', [])
            self.lr_history = checkpoint.get('lr_history', [])
            self.best_val_loss = checkpoint.get('best_val_loss', float('inf'))
            self.epochs_without_improvement = checkpoint.get('epochs_without_improvement', 0)
            self.current_file_index = checkpoint.get('current_file_index', 0)
            
            # Load configuration if available
            if 'config' in checkpoint:
                self.config.from_dict(checkpoint['config'])
                # Update GUI
                for key, entry in self.param_entries.items():
                    if hasattr(self.config, key):
                        entry.delete(0, tk.END)
                        entry.insert(0, str(getattr(self.config, key)))

                # Update new epoch-based saving parameters
                self.save_epochs_entry.delete(0, tk.END)
                self.save_epochs_entry.insert(0, ",".join(map(str, self.config.save_model_epochs)))
                self.auto_save_best_var.set(self.config.auto_save_best)
                self.auto_load_checkpoint_var.set(self.config.auto_load_checkpoint)

            self.status_label.config(text=f"Loaded checkpoint from epoch {self.epochs_completed}")
            self.update_plot()
            
            logger.info(f"Checkpoint loaded successfully from {file_path}")
            
        except Exception as e:
            messagebox.showerror("Error", f"Failed to load checkpoint: {e}")
            logger.error(f"Checkpoint loading failed: {e}")

    def auto_load_latest_checkpoint(self) -> bool:
        """Automatically load the most recent checkpoint if available.

        Returns:
            bool: True if a checkpoint was loaded successfully, False otherwise.
        """
        try:
            # Find all checkpoint files
            checkpoints = list(self.checkpoint_folder.glob("checkpoint_epoch_*.pt"))

            if not checkpoints:
                logger.info("No checkpoints found for auto-loading")
                return False

            # Sort by modification time (most recent first)
            checkpoints.sort(key=lambda x: x.stat().st_mtime, reverse=True)
            latest_checkpoint = checkpoints[0]

            logger.info(f"Auto-loading latest checkpoint: {latest_checkpoint}")

            # Load the checkpoint
            checkpoint = torch.load(latest_checkpoint, map_location=self.device)

            # Initialize model if needed
            self.initialize_model()

            # Load states
            self.model.load_state_dict(checkpoint['model_state_dict'])
            self.optimizer.load_state_dict(checkpoint['optimizer_state_dict'])

            if 'scheduler_state_dict' in checkpoint and checkpoint['scheduler_state_dict']:
                self.scheduler.load_state_dict(checkpoint['scheduler_state_dict'])

            # Restore training history
            self.epochs_completed = checkpoint.get('epoch', 0)
            self.train_loss_history = checkpoint.get('train_loss_history', [])
            self.val_loss_history = checkpoint.get('val_loss_history', [])
            self.lr_history = checkpoint.get('lr_history', [])
            self.best_val_loss = checkpoint.get('best_val_loss', float('inf'))
            self.epochs_without_improvement = checkpoint.get('epochs_without_improvement', 0)
            self.current_file_index = checkpoint.get('current_file_index', 0)

            # Load configuration if available
            if 'config' in checkpoint:
                self.config.from_dict(checkpoint['config'])
                # Update GUI
                for key, entry in self.param_entries.items():
                    if hasattr(self.config, key):
                        entry.delete(0, tk.END)
                        entry.insert(0, str(getattr(self.config, key)))

                # Update new epoch-based saving parameters
                self.save_epochs_entry.delete(0, tk.END)
                self.save_epochs_entry.insert(0, ",".join(map(str, self.config.save_model_epochs)))
                self.auto_save_best_var.set(self.config.auto_save_best)
                self.auto_load_checkpoint_var.set(self.config.auto_load_checkpoint)

            # Update plots
            self.update_plot()

            logger.info(f"Auto-loaded checkpoint from epoch {self.epochs_completed}")
            return True

        except Exception as e:
            logger.error(f"Failed to auto-load checkpoint: {e}")
            return False

    def load_latest_checkpoint_manual(self):
        """Manually load the latest checkpoint with user feedback."""
        if self.auto_load_latest_checkpoint():
            messagebox.showinfo("Success", f"Latest checkpoint loaded successfully!\nResuming from epoch {self.epochs_completed}")
        else:
            messagebox.showwarning("No Checkpoint", "No checkpoints found to load.")

    def reset_file_progress(self):
        """Reset file processing progress to start from the beginning."""
        if self.training_files:
            self.current_file_index = 0
            self.status_label.config(text=f"File progress reset - will process all {len(self.training_files)} files from beginning")
            logger.info("File processing progress reset to 0")
            messagebox.showinfo("Reset Complete", f"File progress reset!\nWill process all {len(self.training_files)} training files from the beginning.")
        else:
            messagebox.showwarning("No Files", "No training files selected. Please select a training folder first.")

    def export_model(self):
        """Export trained model for inference."""
        if not self.model:
            messagebox.showerror("Error", "No model to export!")
            return
        
        file_path = filedialog.asksaveasfilename(
            defaultextension=".pt",
            filetypes=[("PyTorch files", "*.pt"), ("All files", "*.*")]
        )
        
        if file_path:
            try:
                # Export model for inference
                export_data = {
                    'model_state_dict': self.model.state_dict(),
                    'model_class': 'ImprovedNNUE',
                    'input_size': 776,
                    'best_val_loss': self.best_val_loss,
                    'epochs_trained': self.epochs_completed,
                    'export_timestamp': datetime.now().isoformat(),
                    'config': self.config.to_dict()
                }
                
                torch.save(export_data, file_path)
                self.status_label.config(text=f"Model exported to {file_path}")
                logger.info(f"Model exported to {file_path}")
                
            except Exception as e:
                messagebox.showerror("Error", f"Failed to export model: {e}")
    
    def import_model(self):
        """Import a trained model."""
        file_path = filedialog.askopenfilename(
            filetypes=[("PyTorch files", "*.pt"), ("All files", "*.*")]
        )
        
        if file_path:
            try:
                model_data = torch.load(file_path, map_location=self.device)
                
                # Initialize model
                self.initialize_model()
                
                # Load model state
                self.model.load_state_dict(model_data['model_state_dict'])
                
                # Update status
                epochs = model_data.get('epochs_trained', 'Unknown')
                val_loss = model_data.get('best_val_loss', 'Unknown')
                
                self.status_label.config(text=f"Model imported - Epochs: {epochs}, Best Val Loss: {val_loss}")
                logger.info(f"Model imported from {file_path}")
                
            except Exception as e:
                messagebox.showerror("Error", f"Failed to import model: {e}")
    
    def evaluate_position(self):
        """Evaluate a chess position using the trained model."""
        if not self.model:
            messagebox.showerror("Error", "No trained model available!")
            return
        
        fen = self.fen_entry.get().strip()
        if not fen:
            messagebox.showerror("Error", "Please enter a FEN string!")
            return
        
        try:
            # Parse FEN and create features
            board = chess.Board(fen)
            dataset = ChessDataset.__new__(ChessDataset)  # Create without __init__
            dataset.piece_to_idx = {
                'P': 0, 'N': 1, 'B': 2, 'R': 3, 'Q': 4, 'K': 5,
                'p': 6, 'n': 7, 'b': 8, 'r': 9, 'q': 10, 'k': 11
            }
            
            features = dataset.fen_to_features(board)
            features_tensor = torch.FloatTensor(features).unsqueeze(0).to(self.device)
            
            # Evaluate
            self.model.eval()
            with torch.no_grad():
                evaluation = self.model(features_tensor).item()
            
            # Format result
            if evaluation > 0:
                result = f"Evaluation: +{evaluation:.3f} (White advantage)"
            elif evaluation < 0:
                result = f"Evaluation: {evaluation:.3f} (Black advantage)"
            else:
                result = f"Evaluation: {evaluation:.3f} (Equal position)"
            
            self.eval_result_label.config(text=result)
            
        except Exception as e:
            messagebox.showerror("Error", f"Failed to evaluate position: {e}")
    
    def show_model_info(self):
        """Display model information."""
        if not self.model:
            messagebox.showinfo("Model Info", "No model loaded")
            return
        
        # Count parameters
        total_params = sum(p.numel() for p in self.model.parameters())
        trainable_params = sum(p.numel() for p in self.model.parameters() if p.requires_grad)
        
        # Model info
        info = f"""Model Information:
        
Architecture: Enhanced NNUE
Total Parameters: {total_params:,}
Trainable Parameters: {trainable_params:,}
Device: {self.device}
Epochs Completed: {self.epochs_completed}
Best Validation Loss: {self.best_val_loss:.6f}

Current Learning Rate: {self.optimizer.param_groups[0]['lr']:.6f}
Training Files Processed: {self.current_file_index}/{len(self.training_files)}
"""
        
        messagebox.showinfo("Model Information", info)

    def show_saved_models_info(self):
        """Display information about saved epoch models."""
        try:
            # Find all saved epoch models
            epoch_models = sorted(self.checkpoint_folder.glob("model_epoch_*.pt"))
            best_model = self.checkpoint_folder / "best_model.pt"

            if not epoch_models and not best_model.exists():
                messagebox.showinfo("Saved Models", "No saved models found.")
                return

            info_lines = ["Saved Models Information:\n"]

            # Best model info
            if best_model.exists():
                try:
                    best_data = torch.load(best_model, map_location='cpu')
                    info_lines.append(f"Best Model:")
                    info_lines.append(f"  - Epoch: {best_data.get('epoch', 'Unknown')}")
                    info_lines.append(f"  - Validation Loss: {best_data.get('val_loss', 'Unknown'):.6f}")
                    info_lines.append(f"  - File: {best_model.name}\n")
                except Exception as e:
                    info_lines.append(f"Best Model: Error loading ({e})\n")

            # Epoch models info
            if epoch_models:
                info_lines.append("Epoch-based Models:")
                for model_path in epoch_models:
                    try:
                        model_data = torch.load(model_path, map_location='cpu')
                        epoch = model_data.get('epoch', 'Unknown')
                        val_loss = model_data.get('val_loss', 'N/A')
                        train_loss = model_data.get('train_loss', 'N/A')
                        timestamp = model_data.get('timestamp', 'Unknown')

                        info_lines.append(f"  - Epoch {epoch}:")
                        info_lines.append(f"    Train Loss: {train_loss:.6f}" if train_loss != 'N/A' else "    Train Loss: N/A")
                        info_lines.append(f"    Val Loss: {val_loss:.6f}" if val_loss != 'N/A' else "    Val Loss: N/A")
                        info_lines.append(f"    Saved: {timestamp[:19] if timestamp != 'Unknown' else 'Unknown'}")
                        info_lines.append(f"    File: {model_path.name}")
                        info_lines.append("")
                    except Exception as e:
                        info_lines.append(f"  - {model_path.name}: Error loading ({e})")

            # Configuration info
            info_lines.append(f"\nCurrent Configuration:")
            info_lines.append(f"  - Save at epochs: {self.config.save_model_epochs}")
            info_lines.append(f"  - Auto-save best: {self.config.auto_save_best}")

            messagebox.showinfo("Saved Models Information", "\n".join(info_lines))

        except Exception as e:
            messagebox.showerror("Error", f"Failed to get saved models info: {e}")

    def run_validation(self):
        """Run validation manually."""
        val_file = self.val_file_entry.get()
        if not val_file or not os.path.exists(val_file):
            messagebox.showerror("Error", "Please select a valid validation file!")
            return
        
        if not self.model:
            messagebox.showerror("Error", "No model available for validation!")
            return
        
        try:
            self.model.eval()
            dataset = ChessDataset(val_file)
            val_loader = DataLoader(dataset, batch_size=self.config.batch_size, num_workers=2)
            
            val_loss = 0.0
            num_batches = 0
            predictions = []
            targets = []
            
            with torch.no_grad():
                for features, scores in val_loader:
                    features, scores = features.to(self.device), scores.to(self.device)
                    outputs = self.model(features)
                    val_loss += self.criterion(outputs, scores).item()
                    
                    # Collect for analysis
                    predictions.extend(outputs.cpu().numpy().flatten())
                    targets.extend(scores.cpu().numpy().flatten())
                    num_batches += 1
            
            avg_val_loss = val_loss / max(num_batches, 1)
            
            # Calculate additional metrics
            predictions = np.array(predictions)
            targets = np.array(targets)
            
            mae = np.mean(np.abs(predictions - targets))
            correlation = np.corrcoef(predictions, targets)[0, 1] if len(predictions) > 1 else 0
            
            result = f"""Validation Results:
            
MSE Loss: {avg_val_loss:.6f}
MAE: {mae:.6f}
Correlation: {correlation:.4f}
Samples: {len(predictions)}

Prediction Range: [{predictions.min():.3f}, {predictions.max():.3f}]
Target Range: [{targets.min():.3f}, {targets.max():.3f}]
"""
            
            messagebox.showinfo("Validation Results", result)
            self.status_label.config(text=f"Validation completed - MSE: {avg_val_loss:.6f}")
            
        except Exception as e:
            messagebox.showerror("Error", f"Validation failed: {e}")
            logger.error(f"Validation error: {e}")
    
    # File selection methods
    def select_train_folder(self):
        """Select training folder."""
        folder = filedialog.askdirectory(title="Select Training CSV Folder")
        if folder:
            self.train_folder_entry.delete(0, tk.END)
            self.train_folder_entry.insert(0, folder)

            # Get new training files
            new_training_files = sorted(glob.glob(os.path.join(folder, "*.csv")))

            # Check if this is a different set of files than before
            if new_training_files != self.training_files:
                self.training_files = new_training_files
                self.current_file_index = 0  # Reset to start from beginning with new files
                logger.info(f"New training files detected, reset file index to 0")
            else:
                self.training_files = new_training_files
                # Keep current file index if same files (allows resuming)
                logger.info(f"Same training files detected, keeping current file index: {self.current_file_index}")

            self.status_label.config(text=f"Found {len(self.training_files)} training files (starting from index {self.current_file_index})")
            logger.info(f"Selected training folder with {len(self.training_files)} files, current index: {self.current_file_index}")
    
    def select_processed_folder(self):
        """Select processed folder."""
        folder = filedialog.askdirectory(title="Select Processed Files Folder")
        if folder:
            self.processed_folder_entry.delete(0, tk.END)
            self.processed_folder_entry.insert(0, folder)
    
    def select_val_file(self):
        """Select validation file."""
        file_path = filedialog.askopenfilename(
            title="Select Validation CSV File",
            filetypes=[("CSV files", "*.csv"), ("All files", "*.*")]
        )
        if file_path:
            self.val_file_entry.delete(0, tk.END)
            self.val_file_entry.insert(0, file_path)

def main():
    """Main application entry point."""
    try:
        root = tk.Tk()
        app = NNUE_Trainer_App(root)
        
        # Set up proper cleanup on close
        def on_closing():
            if app.training_thread and app.training_thread.is_alive():
                app.stop_training_flag.set()
                app.training_thread.join(timeout=2.0)
            root.destroy()
        
        root.protocol("WM_DELETE_WINDOW", on_closing)
        root.mainloop()
        
    except Exception as e:
        logger.error(f"Application error: {e}")
        messagebox.showerror("Fatal Error", f"Application failed to start: {e}")

if __name__ == '__main__':
    main()