#!/usr/bin/env python3
"""
Test script to verify the epoch-based saving functionality.
This script tests the configuration changes without requiring PyTorch.
"""

import sys
import os

# Add the current directory to the path to import our modules
sys.path.insert(0, os.path.dirname(os.path.abspath(__file__)))

def test_training_config():
    """Test the TrainingConfig class with new epoch-based saving parameters."""
    print("Testing TrainingConfig class...")
    
    # Mock the required imports to avoid PyTorch dependency
    import types
    
    # Create a mock module for the imports we can't load
    mock_torch = types.ModuleType('torch')
    mock_torch.nn = types.ModuleType('torch.nn')
    mock_torch.optim = types.ModuleType('torch.optim')
    mock_torch.device = lambda x: x
    mock_torch.cuda = types.ModuleType('torch.cuda')
    mock_torch.cuda.is_available = lambda: False
    
    sys.modules['torch'] = mock_torch
    sys.modules['torch.nn'] = mock_torch.nn
    sys.modules['torch.optim'] = mock_torch.optim
    sys.modules['torch.utils'] = types.ModuleType('torch.utils')
    sys.modules['torch.utils.data'] = types.ModuleType('torch.utils.data')
    
    # Mock other dependencies
    sys.modules['chess'] = types.ModuleType('chess')
    
    try:
        # Import our TrainingConfig class
        from newNNue import TrainingConfig
        
        # Test default configuration
        config = TrainingConfig()
        
        print(f"✓ Default save_model_epochs: {config.save_model_epochs}")
        print(f"✓ Default auto_save_best: {config.auto_save_best}")
        
        # Test configuration serialization
        config_dict = config.to_dict()
        print(f"✓ Configuration serialized successfully")
        print(f"  - save_model_epochs in dict: {'save_model_epochs' in config_dict}")
        print(f"  - auto_save_best in dict: {'auto_save_best' in config_dict}")
        
        # Test configuration deserialization
        new_config = TrainingConfig()
        test_dict = {
            'save_model_epochs': [25, 75, 150, 300],
            'auto_save_best': True,
            'epochs_per_file': 15
        }
        new_config.from_dict(test_dict)
        
        print(f"✓ Configuration deserialized successfully")
        print(f"  - Updated save_model_epochs: {new_config.save_model_epochs}")
        print(f"  - Updated auto_save_best: {new_config.auto_save_best}")
        print(f"  - Updated epochs_per_file: {new_config.epochs_per_file}")
        
        # Test edge cases
        edge_config = TrainingConfig()
        edge_dict = {
            'save_model_epochs': [],  # Empty list
            'auto_save_best': False,
            'nonexistent_param': 'should_be_ignored'
        }
        edge_config.from_dict(edge_dict)
        
        print(f"✓ Edge case handling successful")
        print(f"  - Empty save_model_epochs: {edge_config.save_model_epochs}")
        print(f"  - auto_save_best: {edge_config.auto_save_best}")
        
        return True
        
    except Exception as e:
        print(f"✗ Error testing TrainingConfig: {e}")
        return False

def test_epoch_saving_logic():
    """Test the epoch-based saving logic."""
    print("\nTesting epoch-based saving logic...")
    
    # Test epochs to save
    save_epochs = [50, 100, 200, 500, 1000]
    current_epochs = [1, 25, 49, 50, 51, 99, 100, 101, 199, 200, 201, 499, 500, 501, 999, 1000, 1001]
    
    expected_saves = [50, 100, 200, 500, 1000]
    actual_saves = [epoch for epoch in current_epochs if epoch in save_epochs]
    
    print(f"✓ Save epochs: {save_epochs}")
    print(f"✓ Test epochs: {current_epochs}")
    print(f"✓ Expected saves: {expected_saves}")
    print(f"✓ Actual saves: {actual_saves}")
    
    if actual_saves == expected_saves:
        print("✓ Epoch-based saving logic works correctly")
        return True
    else:
        print("✗ Epoch-based saving logic failed")
        return False

def main():
    """Run all tests."""
    print("=" * 60)
    print("Testing Epoch-Based Model Saving Functionality")
    print("=" * 60)
    
    tests_passed = 0
    total_tests = 2
    
    # Test 1: TrainingConfig
    if test_training_config():
        tests_passed += 1
    
    # Test 2: Epoch saving logic
    if test_epoch_saving_logic():
        tests_passed += 1
    
    print("\n" + "=" * 60)
    print(f"Test Results: {tests_passed}/{total_tests} tests passed")
    
    if tests_passed == total_tests:
        print("✓ All tests passed! The epoch-based saving functionality is working correctly.")
        return 0
    else:
        print("✗ Some tests failed. Please check the implementation.")
        return 1

if __name__ == "__main__":
    sys.exit(main())
